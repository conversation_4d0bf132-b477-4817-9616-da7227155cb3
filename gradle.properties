# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -Xms256m -XX:MaxMetaspaceSize=256m -XX:+HeapDumpOnOutOfMemoryError
# Bugs: This will override important JVM argument defaults. Include all important JVM arguments if you wan to change arguments. See https://github.com/gradle/gradle/issues/19750
org.gradle.jvmargs=-XX:+UseParallelGC -Xmx6g -Xms256m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=false
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# kotlin.incremental.useClasspathSnapshot=true  # Deprecated property, removed
android.enableResourceOptimizations=true
# android.enableNewResourceShrinker' is deprecated.
# It was removed in version 8.0 of the Android Gradle plugin.
# android.enableNewResourceShrinker=true
android.experimental.enableNewResourceShrinker.preciseShrinking=true
# https://blog.gradle.org/introducing-file-system-watching
org.gradle.vfs.watch=true
# enable cache can not up app version
org.gradle.unsafe.configuration-cache=false
# Disable buildFeatures flags by default
android.defaults.buildfeatures.aidl=false
android.defaults.buildfeatures.buildconfig=false
android.defaults.buildfeatures.renderscript=false
android.defaults.buildfeatures.resvalues=false
android.defaults.buildfeatures.shaders=false
# enables namespacing of each library's R class so that its R class includes only the resources declared in the library itself
# and none from the library's dependencies, thereby reducing the size of the R class for that library.
android.nonTransitiveRClass=true
# https://chromiumdash.appspot.com/releases?platform=Android
CronetVersion=128.0.6613.40
CronetMainVersion=*********
android.injected.testOnly=false
android.nonFinalResIds=true
android.injected.androidTest.leaveApksInstalledAfterRun=true
